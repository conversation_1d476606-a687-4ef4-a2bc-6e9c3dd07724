
 Clicking layer: layer_1753641696022 Current active: null
 🎯 handleLayerActivation called with: layer_1753641696022
 🎯 Previous activeLayerId: null
 🎯 Setting activeLayerId to: layer_1753641696022
 Cleaning up event listeners
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 🔥 External active layer changed: layer_1753641696022
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 6
 All canvas objects: Array(6)
 🔍 Looking for object with ID: layer_1753641696022
 🔍 Found target object: klass
 🔍 Object details: Object
 Selecting object in canvas: layer_1753641696022
 Cleaning up event listeners
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 🔥 External active layer changed: layer_1753641696022
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 6
 All canvas objects: Array(6)
 🔍 Looking for object with ID: layer_1753641696022
 🔍 Found target object: klass
 🔍 Object details: Object
 Selecting object in canvas: layer_1753641696022
 Applying customizations: Object
 Template editable layers: Array(2)
 Processing layer layer_1753599148950 (text): Heading
 Skipping layer layer_1753599148950 - no custom value or same as original
 Processing layer layer_1753641696022 (image): undefined
 Skipping layer layer_1753641696022 - no custom value or same as original
 Applying customizations: Object
 Template editable layers: Array(2)
 Processing layer layer_1753599148950 (text): Heading
 Skipping layer layer_1753599148950 - no custom value or same as original
 Processing layer layer_1753641696022 (image): undefined
 Skipping layer layer_1753641696022 - no custom value or same as original
 ⚠️ Object already has ID: layer_1753599148950 for layer layer_1753599148950
 ⚠️ Object already has ID: layer_1753641696022 for layer layer_1753641696022
 Locking non-editable objects...
 🏠 Workspace object: rect (keeping default properties)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753641696022)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: textbox (ID: layer_1753599148950)
 ⚠️ Object already has ID: layer_1753599148950 for layer layer_1753599148950
 ⚠️ Object already has ID: layer_1753641696022 for layer layer_1753641696022
 Locking non-editable objects...
 🏠 Workspace object: rect (keeping default properties)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753641696022)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: textbox (ID: layer_1753599148950)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
 ID Assignment Verification: Object
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
 ID Assignment Verification: Object
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
 Cleaning up event listeners
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 🔥 External active layer changed: layer_1753641696022
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 6
 All canvas objects: Array(6)
 🔍 Looking for object with ID: layer_1753641696022
 🔍 Found target object: klass
 🔍 Object details: Object
 Selecting object in canvas: layer_1753641696022
 Cleaning up event listeners
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 🔥 External active layer changed: layer_1753641696022
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 6
 All canvas objects: Array(6)
 🔍 Looking for object with ID: layer_1753641696022
 🔍 Found target object: klass
 🔍 Object details: Object
 Selecting object in canvas: layer_1753641696022
 Cleaning up event listeners
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 🔥 External active layer changed: layer_1753641696022
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 6
 All canvas objects: Array(6)
 🔍 Looking for object with ID: layer_1753641696022
 🔍 Found target object: klass
 🔍 Object details: Object
 Selecting object in canvas: layer_1753641696022
 Applying customizations: Object
 Template editable layers: Array(2)
 Processing layer layer_1753599148950 (text): Heading
 Skipping layer layer_1753599148950 - no custom value or same as original
 Processing layer layer_1753641696022 (image): undefined
 Skipping layer layer_1753641696022 - no custom value or same as original
 Applying customizations: Object
 Template editable layers: Array(2)
 Processing layer layer_1753599148950 (text): Heading
 Skipping layer layer_1753599148950 - no custom value or same as original
 Processing layer layer_1753641696022 (image): undefined
 Skipping layer layer_1753641696022 - no custom value or same as original
 Cleaning up event listeners
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 🔥 External active layer changed: layer_1753641696022
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 6
 All canvas objects: Array(6)
 🔍 Looking for object with ID: layer_1753641696022
 🔍 Found target object: klass
 🔍 Object details: Object
 Selecting object in canvas: layer_1753641696022
 Applying customizations: Object
 Template editable layers: Array(2)
 Processing layer layer_1753599148950 (text): Heading
 Skipping layer layer_1753599148950 - no custom value or same as original
 Processing layer layer_1753641696022 (image): undefined
 Skipping layer layer_1753641696022 - no custom value or same as original
 ⚠️ Object already has ID: layer_1753599148950 for layer layer_1753599148950
 ⚠️ Object already has ID: layer_1753641696022 for layer layer_1753641696022
 Locking non-editable objects...
 🏠 Workspace object: rect (keeping default properties)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753641696022)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: textbox (ID: layer_1753599148950)
 Applying customizations: Object
 Template editable layers: Array(2)
 Processing layer layer_1753599148950 (text): Heading
 Skipping layer layer_1753599148950 - no custom value or same as original
 Processing layer layer_1753641696022 (image): undefined
 Skipping layer layer_1753641696022 - no custom value or same as original
 Cleaning up event listeners
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 🔥 External active layer changed: layer_1753641696022
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 6
 All canvas objects: Array(6)
 🔍 Looking for object with ID: layer_1753641696022
 🔍 Found target object: klass
 🔍 Object details: Object
 Selecting object in canvas: layer_1753641696022
 ⚠️ Object already has ID: layer_1753599148950 for layer layer_1753599148950
 ⚠️ Object already has ID: layer_1753641696022 for layer layer_1753641696022
 Locking non-editable objects...
 🏠 Workspace object: rect (keeping default properties)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753641696022)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: textbox (ID: layer_1753599148950)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
 ID Assignment Verification: Object
 ⚠️ Object already has ID: layer_1753599148950 for layer layer_1753599148950
 ⚠️ Object already has ID: layer_1753641696022 for layer layer_1753641696022
 Locking non-editable objects...
 🏠 Workspace object: rect (keeping default properties)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753641696022)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: textbox (ID: layer_1753599148950)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
 ID Assignment Verification: Object
 Applying customizations: Object
 Template editable layers: Array(2)
 Processing layer layer_1753599148950 (text): Heading
 Skipping layer layer_1753599148950 - no custom value or same as original
 Processing layer layer_1753641696022 (image): undefined
 Skipping layer layer_1753641696022 - no custom value or same as original
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
 ID Assignment Verification: Object
 ⚠️ Object already has ID: layer_1753599148950 for layer layer_1753599148950
 ⚠️ Object already has ID: layer_1753641696022 for layer layer_1753641696022
 Locking non-editable objects...
 🏠 Workspace object: rect (keeping default properties)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753641696022)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: textbox (ID: layer_1753599148950)
 Cleaning up event listeners
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 🔥 External active layer changed: layer_1753641696022
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 6
 All canvas objects: Array(6)
 🔍 Looking for object with ID: layer_1753641696022
 🔍 Found target object: klass
 🔍 Object details: Object
 Selecting object in canvas: layer_1753641696022
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
 ID Assignment Verification: Object
 Applying customizations: Object
 Template editable layers: Array(2)
 Processing layer layer_1753599148950 (text): Heading
 Skipping layer layer_1753599148950 - no custom value or same as original
 Processing layer layer_1753641696022 (image): undefined
 Skipping layer layer_1753641696022 - no custom value or same as original
 Cleaning up event listeners
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 🔥 External active layer changed: layer_1753641696022
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 6
 All canvas objects: Array(6)
 🔍 Looking for object with ID: layer_1753641696022
 🔍 Found target object: klass
 🔍 Object details: Object
 Selecting object in canvas: layer_1753641696022
 ⚠️ Object already has ID: layer_1753599148950 for layer layer_1753599148950
 ⚠️ Object already has ID: layer_1753641696022 for layer layer_1753641696022
 Locking non-editable objects...
 🏠 Workspace object: rect (keeping default properties)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753641696022)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: textbox (ID: layer_1753599148950)
 Cleaning up event listeners
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 🔥 External active layer changed: layer_1753641696022
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 6
 All canvas objects: Array(6)
 🔍 Looking for object with ID: layer_1753641696022
 🔍 Found target object: klass
 🔍 Object details: Object
 Selecting object in canvas: layer_1753641696022
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
 ID Assignment Verification: Object
 Applying customizations: Object
 Template editable layers: Array(2)
 Processing layer layer_1753599148950 (text): Heading
 Skipping layer layer_1753599148950 - no custom value or same as original
 Processing layer layer_1753641696022 (image): undefined
 Skipping layer layer_1753641696022 - no custom value or same as original
 ⚠️ Object already has ID: layer_1753599148950 for layer layer_1753599148950
 ⚠️ Object already has ID: layer_1753641696022 for layer layer_1753641696022
 Locking non-editable objects...
 🏠 Workspace object: rect (keeping default properties)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753641696022)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: textbox (ID: layer_1753599148950)
 Applying customizations: Object
 Template editable layers: Array(2)
 Processing layer layer_1753599148950 (text): Heading
 Skipping layer layer_1753599148950 - no custom value or same as original
 Processing layer layer_1753641696022 (image): undefined
 Skipping layer layer_1753641696022 - no custom value or same as original
 Cleaning up event listeners
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 🔥 External active layer changed: layer_1753641696022
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 6
 All canvas objects: Array(6)
 🔍 Looking for object with ID: layer_1753641696022
 🔍 Found target object: klass
 🔍 Object details: Object
 Selecting object in canvas: layer_1753641696022
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
 ID Assignment Verification: Object
 ⚠️ Object already has ID: layer_1753599148950 for layer layer_1753599148950
 ⚠️ Object already has ID: layer_1753641696022 for layer layer_1753641696022
 Locking non-editable objects...
 🏠 Workspace object: rect (keeping default properties)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753641696022)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: textbox (ID: layer_1753599148950)
 Cleaning up event listeners
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 🔥 External active layer changed: layer_1753641696022
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 6
 All canvas objects: Array(6)
 🔍 Looking for object with ID: layer_1753641696022
 🔍 Found target object: klass
 🔍 Object details: Object
 Selecting object in canvas: layer_1753641696022
 Applying customizations: Object
 Template editable layers: Array(2)
 Processing layer layer_1753599148950 (text): Heading
 Skipping layer layer_1753599148950 - no custom value or same as original
 Processing layer layer_1753641696022 (image): undefined
 Skipping layer layer_1753641696022 - no custom value or same as original
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
 ID Assignment Verification: Object
 ⚠️ Object already has ID: layer_1753599148950 for layer layer_1753599148950
 ⚠️ Object already has ID: layer_1753641696022 for layer layer_1753641696022
 Locking non-editable objects...
 🏠 Workspace object: rect (keeping default properties)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753641696022)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: textbox (ID: layer_1753599148950)
 Applying customizations: Object
 Template editable layers: Array(2)
 Processing layer layer_1753599148950 (text): Heading
 Skipping layer layer_1753599148950 - no custom value or same as original
 Processing layer layer_1753641696022 (image): undefined
 Skipping layer layer_1753641696022 - no custom value or same as original
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
 ID Assignment Verification: Object
 ⚠️ Object already has ID: layer_1753599148950 for layer layer_1753599148950
 ⚠️ Object already has ID: layer_1753641696022 for layer layer_1753641696022
 Locking non-editable objects...
 🏠 Workspace object: rect (keeping default properties)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753641696022)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: textbox (ID: layer_1753599148950)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
 ID Assignment Verification: Object
 ⚠️ Object already has ID: layer_1753599148950 for layer layer_1753599148950
 ⚠️ Object already has ID: layer_1753641696022 for layer layer_1753641696022
 Locking non-editable objects...
 🏠 Workspace object: rect (keeping default properties)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753641696022)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: textbox (ID: layer_1753599148950)
 Cleaning up event listeners
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 🔥 External active layer changed: layer_1753641696022
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 6
 All canvas objects: Array(6)
 🔍 Looking for object with ID: layer_1753641696022
 🔍 Found target object: klass
 🔍 Object details: Object
 Selecting object in canvas: layer_1753641696022
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
 ID Assignment Verification: Object
 Cleaning up event listeners
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 🔥 External active layer changed: layer_1753641696022
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 6
 All canvas objects: Array(6)
 🔍 Looking for object with ID: layer_1753641696022
 🔍 Found target object: klass
 🔍 Object details: Object
 Selecting object in canvas: layer_1753641696022
 Cleaning up event listeners
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 🔥 External active layer changed: layer_1753641696022
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 6
 All canvas objects: Array(6)
 🔍 Looking for object with ID: layer_1753641696022
 🔍 Found target object: klass
 🔍 Object details: Object
 Selecting object in canvas: layer_1753641696022
 Applying customizations: Object
 Template editable layers: Array(2)
 Processing layer layer_1753599148950 (text): Heading
 Skipping layer layer_1753599148950 - no custom value or same as original
 Processing layer layer_1753641696022 (image): undefined
 Skipping layer layer_1753641696022 - no custom value or same as original
 Applying customizations: Object
 Template editable layers: Array(2)
 Processing layer layer_1753599148950 (text): Heading
 Skipping layer layer_1753599148950 - no custom value or same as original
 Processing layer layer_1753641696022 (image): undefined
 Skipping layer layer_1753641696022 - no custom value or same as original
 Applying customizations: Object
 Template editable layers: Array(2)
 Processing layer layer_1753599148950 (text): Heading
 Skipping layer layer_1753599148950 - no custom value or same as original
 Processing layer layer_1753641696022 (image): undefined
 Skipping layer layer_1753641696022 - no custom value or same as original
 ⚠️ Object already has ID: layer_1753599148950 for layer layer_1753599148950
 ⚠️ Object already has ID: layer_1753641696022 for layer layer_1753641696022
 Locking non-editable objects...
 🏠 Workspace object: rect (keeping default properties)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753641696022)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: textbox (ID: layer_1753599148950)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
 ID Assignment Verification: Object
 Cleaning up event listeners
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 🔥 External active layer changed: layer_1753641696022
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 6
 All canvas objects: Array(6)
 🔍 Looking for object with ID: layer_1753641696022
 🔍 Found target object: klass
 🔍 Object details: Object
 Selecting object in canvas: layer_1753641696022
 ⚠️ Object already has ID: layer_1753599148950 for layer layer_1753599148950
 ⚠️ Object already has ID: layer_1753641696022 for layer layer_1753641696022
 Locking non-editable objects...
 🏠 Workspace object: rect (keeping default properties)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753641696022)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: textbox (ID: layer_1753599148950)
 ⚠️ Object already has ID: layer_1753599148950 for layer layer_1753599148950
 ⚠️ Object already has ID: layer_1753641696022 for layer layer_1753641696022
 Locking non-editable objects...
 🏠 Workspace object: rect (keeping default properties)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753641696022)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: textbox (ID: layer_1753599148950)
 Cleaning up event listeners
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 🔥 External active layer changed: layer_1753641696022
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 6
 All canvas objects: Array(6)
 🔍 Looking for object with ID: layer_1753641696022
 🔍 Found target object: klass
 🔍 Object details: Object
 Selecting object in canvas: layer_1753641696022
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
 ID Assignment Verification: Object
 Applying customizations: Object
 Template editable layers: Array(2)
 Processing layer layer_1753599148950 (text): Heading
 Skipping layer layer_1753599148950 - no custom value or same as original
 Processing layer layer_1753641696022 (image): undefined
 Skipping layer layer_1753641696022 - no custom value or same as original
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
 ID Assignment Verification: Object
 Applying customizations: Object
 Template editable layers: Array(2)
 Processing layer layer_1753599148950 (text): Heading
 Skipping layer layer_1753599148950 - no custom value or same as original
 Processing layer layer_1753641696022 (image): undefined
 Skipping layer layer_1753641696022 - no custom value or same as original
 Cleaning up event listeners
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 🔥 External active layer changed: layer_1753641696022
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 6
 All canvas objects: Array(6)
 🔍 Looking for object with ID: layer_1753641696022
 🔍 Found target object: klass
 🔍 Object details: Object
 Selecting object in canvas: layer_1753641696022
 ⚠️ Object already has ID: layer_1753599148950 for layer layer_1753599148950
 ⚠️ Object already has ID: layer_1753641696022 for layer layer_1753641696022
 Locking non-editable objects...
 🏠 Workspace object: rect (keeping default properties)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753641696022)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: textbox (ID: layer_1753599148950)
 Applying customizations: Object
 Template editable layers: Array(2)
 Processing layer layer_1753599148950 (text): Heading
 Skipping layer layer_1753599148950 - no custom value or same as original
 Processing layer layer_1753641696022 (image): undefined
 Skipping layer layer_1753641696022 - no custom value or same as original
 Cleaning up event listeners
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 🔥 External active layer changed: layer_1753641696022
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 6
 All canvas objects: Array(6)
 🔍 Looking for object with ID: layer_1753641696022
 🔍 Found target object: klass
 🔍 Object details: Object
 Selecting object in canvas: layer_1753641696022
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
 ID Assignment Verification: Object
 ⚠️ Object already has ID: layer_1753599148950 for layer layer_1753599148950
 ⚠️ Object already has ID: layer_1753641696022 for layer layer_1753641696022
 Locking non-editable objects...
 🏠 Workspace object: rect (keeping default properties)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753641696022)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: textbox (ID: layer_1753599148950)
 Applying customizations: Object
 Template editable layers: Array(2)
 Processing layer layer_1753599148950 (text): Heading
 Skipping layer layer_1753599148950 - no custom value or same as original
 Processing layer layer_1753641696022 (image): undefined
 Skipping layer layer_1753641696022 - no custom value or same as original
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
 ID Assignment Verification: Object
 Cleaning up event listeners
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 🔥 External active layer changed: layer_1753641696022
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 6
 All canvas objects: Array(6)
 🔍 Looking for object with ID: layer_1753641696022
 🔍 Found target object: klass
 🔍 Object details: Object
 Selecting object in canvas: layer_1753641696022
 Cleaning up event listeners
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 🔥 External active layer changed: layer_1753641696022
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 6
 All canvas objects: Array(6)
 🔍 Looking for object with ID: layer_1753641696022
 🔍 Found target object: klass
 🔍 Object details: Object
 Selecting object in canvas: layer_1753641696022
 Cleaning up event listeners
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 🔥 External active layer changed: layer_1753641696022
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 6
 All canvas objects: Array(6)
 🔍 Looking for object with ID: layer_1753641696022
 🔍 Found target object: klass
 🔍 Object details: Object
 Selecting object in canvas: layer_1753641696022
 ⚠️ Object already has ID: layer_1753599148950 for layer layer_1753599148950
 ⚠️ Object already has ID: layer_1753641696022 for layer layer_1753641696022
 Locking non-editable objects...
 🏠 Workspace object: rect (keeping default properties)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753641696022)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: textbox (ID: layer_1753599148950)
 Applying customizations: Object
 Template editable layers: Array(2)
 Processing layer layer_1753599148950 (text): Heading
 Skipping layer layer_1753599148950 - no custom value or same as original
 Processing layer layer_1753641696022 (image): undefined
 Skipping layer layer_1753641696022 - no custom value or same as original
 Cleaning up event listeners
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 🔥 External active layer changed: layer_1753641696022
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 6
 All canvas objects: Array(6)
 🔍 Looking for object with ID: layer_1753641696022
 🔍 Found target object: klass
 🔍 Object details: Object
 Selecting object in canvas: layer_1753641696022
 ⚠️ Object already has ID: layer_1753599148950 for layer layer_1753599148950
 ⚠️ Object already has ID: layer_1753641696022 for layer layer_1753641696022
 Locking non-editable objects...
 🏠 Workspace object: rect (keeping default properties)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753641696022)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: textbox (ID: layer_1753599148950)
 Applying customizations: Object
 Template editable layers: Array(2)
 Processing layer layer_1753599148950 (text): Heading
 Skipping layer layer_1753599148950 - no custom value or same as original
 Processing layer layer_1753641696022 (image): undefined
 Skipping layer layer_1753641696022 - no custom value or same as original
 Applying customizations: Object
 Template editable layers: Array(2)
 Processing layer layer_1753599148950 (text): Heading
 Skipping layer layer_1753599148950 - no custom value or same as original
 Processing layer layer_1753641696022 (image): undefined
 Skipping layer layer_1753641696022 - no custom value or same as original
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
 ID Assignment Verification: Object
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
 ID Assignment Verification: Object
 ⚠️ Object already has ID: layer_1753599148950 for layer layer_1753599148950
 ⚠️ Object already has ID: layer_1753641696022 for layer layer_1753641696022
 Locking non-editable objects...
 🏠 Workspace object: rect (keeping default properties)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753641696022)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: textbox (ID: layer_1753599148950)
 Applying customizations: Object
 Template editable layers: Array(2)
 Processing layer layer_1753599148950 (text): Heading
 Skipping layer layer_1753599148950 - no custom value or same as original
 Processing layer layer_1753641696022 (image): undefined
 Skipping layer layer_1753641696022 - no custom value or same as original
 Cleaning up event listeners
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 🔥 External active layer changed: layer_1753641696022
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 6
 All canvas objects: Array(6)
 🔍 Looking for object with ID: layer_1753641696022
 🔍 Found target object: klass
 🔍 Object details: Object
 Selecting object in canvas: layer_1753641696022
 ⚠️ Object already has ID: layer_1753599148950 for layer layer_1753599148950
 ⚠️ Object already has ID: layer_1753641696022 for layer layer_1753641696022
 Locking non-editable objects...
 🏠 Workspace object: rect (keeping default properties)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753641696022)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: textbox (ID: layer_1753599148950)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
 ID Assignment Verification: Object
 ⚠️ Object already has ID: layer_1753599148950 for layer layer_1753599148950
 ⚠️ Object already has ID: layer_1753641696022 for layer layer_1753641696022
 Locking non-editable objects...
 🏠 Workspace object: rect (keeping default properties)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753641696022)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: textbox (ID: layer_1753599148950)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
 ID Assignment Verification: Object
 Applying customizations: Object
 Template editable layers: Array(2)
 Processing layer layer_1753599148950 (text): Heading
 Skipping layer layer_1753599148950 - no custom value or same as original
 Processing layer layer_1753641696022 (image): undefined
 Skipping layer layer_1753641696022 - no custom value or same as original
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
 ID Assignment Verification: Object
 Cleaning up event listeners
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 🔥 External active layer changed: layer_1753641696022
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 6
 All canvas objects: Array(6)
 🔍 Looking for object with ID: layer_1753641696022
 🔍 Found target object: klass
 🔍 Object details: Object
 Selecting object in canvas: layer_1753641696022
 ⚠️ Object already has ID: layer_1753599148950 for layer layer_1753599148950
 ⚠️ Object already has ID: layer_1753641696022 for layer layer_1753641696022
 Locking non-editable objects...
 🏠 Workspace object: rect (keeping default properties)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753641696022)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: textbox (ID: layer_1753599148950)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
 ID Assignment Verification: Object
 Applying customizations: Object
 Template editable layers: Array(2)
 Processing layer layer_1753599148950 (text): Heading
 Skipping layer layer_1753599148950 - no custom value or same as original
 Processing layer layer_1753641696022 (image): undefined
 Skipping layer layer_1753641696022 - no custom value or same as original
 ⚠️ Object already has ID: layer_1753599148950 for layer layer_1753599148950
 ⚠️ Object already has ID: layer_1753641696022 for layer layer_1753641696022
 Locking non-editable objects...
 🏠 Workspace object: rect (keeping default properties)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753641696022)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: textbox (ID: layer_1753599148950)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
 ID Assignment Verification: Object
 Cleaning up event listeners
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 🔥 External active layer changed: layer_1753641696022
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 6
 All canvas objects: Array(6)
 🔍 Looking for object with ID: layer_1753641696022
 🔍 Found target object: klass
 🔍 Object details: Object
 Selecting object in canvas: layer_1753641696022
 ⚠️ Object already has ID: layer_1753599148950 for layer layer_1753599148950
 ⚠️ Object already has ID: layer_1753641696022 for layer layer_1753641696022
 Locking non-editable objects...
 🏠 Workspace object: rect (keeping default properties)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753641696022)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: textbox (ID: layer_1753599148950)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
 ID Assignment Verification: Object
 Applying customizations: Object
 Template editable layers: Array(2)
 Processing layer layer_1753599148950 (text): Heading
 Skipping layer layer_1753599148950 - no custom value or same as original
 Processing layer layer_1753641696022 (image): undefined
 Skipping layer layer_1753641696022 - no custom value or same as original
 Cleaning up event listeners
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 🔥 External active layer changed: layer_1753641696022
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 6
 All canvas objects: Array(6)
 🔍 Looking for object with ID: layer_1753641696022
 🔍 Found target object: klass
 🔍 Object details: Object
 Selecting object in canvas: layer_1753641696022
 Applying customizations: Object
 Template editable layers: Array(2)
 Processing layer layer_1753599148950 (text): Heading
 Skipping layer layer_1753599148950 - no custom value or same as original
 Processing layer layer_1753641696022 (image): undefined
 Skipping layer layer_1753641696022 - no custom value or same as original
 Cleaning up event listeners
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 🔥 External active layer changed: layer_1753641696022
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 6
 All canvas objects: Array(6)
 🔍 Looking for object with ID: layer_1753641696022
 🔍 Found target object: klass
 🔍 Object details: Object
 Selecting object in canvas: layer_1753641696022
 ⚠️ Object already has ID: layer_1753599148950 for layer layer_1753599148950
 ⚠️ Object already has ID: layer_1753641696022 for layer layer_1753641696022
 Locking non-editable objects...
 🏠 Workspace object: rect (keeping default properties)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753641696022)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: textbox (ID: layer_1753599148950)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
 ID Assignment Verification: Object
 Applying customizations: Object
 Template editable layers: Array(2)
 Processing layer layer_1753599148950 (text): Heading
 Skipping layer layer_1753599148950 - no custom value or same as original
 Processing layer layer_1753641696022 (image): undefined
 Skipping layer layer_1753641696022 - no custom value or same as original
 Cleaning up event listeners
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 🔥 External active layer changed: layer_1753641696022
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 6
 All canvas objects: Array(6)
 🔍 Looking for object with ID: layer_1753641696022
 🔍 Found target object: klass
 🔍 Object details: Object
 Selecting object in canvas: layer_1753641696022
 ⚠️ Object already has ID: layer_1753599148950 for layer layer_1753599148950
 ⚠️ Object already has ID: layer_1753641696022 for layer layer_1753641696022
 Locking non-editable objects...
 🏠 Workspace object: rect (keeping default properties)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753641696022)
 🔒 Locked non-editable object: image (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: textbox (ID: layer_1753599148950)
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:340 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:340 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
customization-editor.tsx:1951 ID Assignment Verification: Object
customization-editor.tsx:1181 Applying customizations: Object
customization-editor.tsx:1182 Template editable layers: Array(2)
customization-editor.tsx:1186 Processing layer layer_1753599148950 (text): Heading
customization-editor.tsx:1190 Skipping layer layer_1753599148950 - no custom value or same as original
customization-editor.tsx:1186 Processing layer layer_1753641696022 (image): undefined
customization-editor.tsx:1190 Skipping layer layer_1753641696022 - no custom value or same as original
customization-editor.tsx:1896 ⚠️ Object already has ID: layer_1753599148950 for layer layer_1753599148950
customization-editor.tsx:1896 ⚠️ Object already has ID: layer_1753641696022 for layer layer_1753641696022
customization-editor.tsx:1903 Locking non-editable objects...
customization-editor.tsx:1933 🏠 Workspace object: rect (keeping default properties)
customization-editor.tsx:1923 🔒 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1931 🔓 Enabled interaction for editable object: image (ID: layer_1753641696022)
customization-editor.tsx:1923 🔒 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1923 🔒 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1931 🔓 Enabled interaction for editable object: textbox (ID: layer_1753599148950)
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:340 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:340 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
customization-editor.tsx:1951 ID Assignment Verification: Object
customization-editor.tsx:1896 ⚠️ Object already has ID: layer_1753599148950 for layer layer_1753599148950
customization-editor.tsx:1896 ⚠️ Object already has ID: layer_1753641696022 for layer layer_1753641696022
customization-editor.tsx:1903 Locking non-editable objects...
customization-editor.tsx:1933 🏠 Workspace object: rect (keeping default properties)
customization-editor.tsx:1923 🔒 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1931 🔓 Enabled interaction for editable object: image (ID: layer_1753641696022)
customization-editor.tsx:1923 🔒 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1923 🔒 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1931 🔓 Enabled interaction for editable object: textbox (ID: layer_1753599148950)
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:340 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:340 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
customization-editor.tsx:1951 ID Assignment Verification: Object
page.tsx:195 🔍 AI Edit Debug: Object
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:340 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:340 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:340 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:340 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:340 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:340 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:340 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:340 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
hot-reloader-client.js:187 [Fast Refresh] rebuilding
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:340 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:340 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:340 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:340 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
page.tsx:195 🔍 AI Edit Debug: {layerId: 'layer_1753641696022', customizations: {…}, hasCustomization: false, customizationValue: undefined}customizationValue: undefinedcustomizations: {layer_1753599148950: 'Heading'}hasCustomization: falselayerId: "layer_1753641696022"[[Prototype]]: Object
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:340 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:340 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:340 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:340 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:340 🔓 Re-enabled interaction for object: image (ID: layer_1753641696022)
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:324 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:340 🔓 Re-enabled interaction for object: textbox (ID: layer_1753599148950)
