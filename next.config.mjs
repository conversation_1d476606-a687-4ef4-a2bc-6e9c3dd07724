/** @type {import('next').NextConfig} */
const nextConfig = {
  // Performance optimizations
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['lucide-react', 'fabric', 'lodash.debounce'],
    serverComponentsExternalPackages: ['@xenova/transformers'],
    // Enable faster development builds
    turbo: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    },
  },

  // Development optimizations
  ...(process.env.NODE_ENV === 'development' && {
    // Faster development builds
    swcMinify: false, // Disable minification in dev
    // Reduce bundle analysis overhead
    webpack: (config, { dev, isServer }) => {
      if (dev) {
        // Faster development builds
        config.optimization = {
          ...config.optimization,
          removeAvailableModules: false,
          removeEmptyChunks: false,
          splitChunks: false,
        };

        // Reduce file watching overhead
        config.watchOptions = {
          poll: false,
          aggregateTimeout: 300,
        };
      }

      // Apply existing webpack config
      return applyWebpackConfig(config, { dev, isServer });
    },
  }),

  // Production optimizations (existing webpack config)
  ...(!process.env.NODE_ENV || process.env.NODE_ENV === 'production' ? {
    webpack: applyWebpackConfig,
  } : {}),

  // Compiler optimizations (disabled for Turbopack compatibility)
  ...(process.env.NODE_ENV === 'production' && !process.env.TURBOPACK && {
    compiler: {
      removeConsole: true,
    },
  }),

  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "images.unsplash.com",
      },
      {
        protocol: "https",
        hostname: "utfs.io",
      },
      {
        protocol: "https",
        hostname: "replicate.delivery",
      },
      {
        protocol: "https",
        hostname: "canva-clone-ali.vercel.app",
      },
      {
        protocol: "https",
        hostname: "api.together.ai",
      },
      {
        protocol: "https",
        hostname: "fal.media",
      },
      {
        protocol: "http",
        hostname: "localhost",
        port: "3000",
      },
      {
        protocol: "http",
        hostname: "localhost",
        port: "3001",
      },
      {
        protocol: "http",
        hostname: "localhost",
        port: "3002",
      },
      {
        protocol: "http",
        hostname: "localhost",
        port: "3003",
      },
      {
        protocol: "https",
        hostname: "clipdrop-api.co",
      },
      {
        protocol: "https",
        hostname: "*.blob.core.windows.net",
      },
      {
        protocol: "https",
        hostname: "*.amazonaws.com",
      },
    ],
    // Image optimization
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60 * 60 * 24 * 7, // 1 week
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },
};

// Extract webpack configuration to reuse
const applyWebpackConfig = (config, { dev, isServer }) => {
    // Production optimizations
    if (!dev) {
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          cacheGroups: {
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              chunks: 'all',
            },
            fabric: {
              test: /[\\/]node_modules[\\/]fabric[\\/]/,
              name: 'fabric',
              chunks: 'all',
            },
            editor: {
              test: /[\\/]src[\\/]features[\\/]editor[\\/]/,
              name: 'editor',
              chunks: 'all',
            },
          },
        },
      };
    }
    // Handle @xenova/transformers
    config.resolve.alias = {
      ...config.resolve.alias,
      "sharp$": false,
      "onnxruntime-node$": false,
    };

    // Exclude problematic packages from bundling
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        crypto: false,
        stream: false,
        util: false,
        buffer: false,
        process: false,
      };
    }

    // Ignore node-specific modules in client-side bundles
    config.externals = config.externals || [];
    config.externals.push({
      'onnxruntime-node': 'onnxruntime-node',
      'sharp': 'sharp',
    });

    return config;
};

export default nextConfig;
