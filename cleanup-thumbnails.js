const fs = require('fs');
const path = require('path');

// <PERSON>ript to clean up excessive thumbnail files
const uploadsDir = path.join(process.cwd(), 'public', 'uploads');

console.log('🧹 Cleaning up excessive thumbnail files...');

try {
  const files = fs.readdirSync(uploadsDir);
  console.log(`Found ${files.length} files in uploads directory`);
  
  // Keep only the most recent 50 files (based on timestamp in filename)
  const sortedFiles = files
    .filter(file => file.endsWith('.jpg') || file.endsWith('.png'))
    .map(file => ({
      name: file,
      timestamp: parseInt(file.split('-')[0]) || 0,
      path: path.join(uploadsDir, file)
    }))
    .sort((a, b) => b.timestamp - a.timestamp);
  
  const filesToKeep = sortedFiles.slice(0, 50);
  const filesToDelete = sortedFiles.slice(50);
  
  console.log(`Keeping ${filesToKeep.length} most recent files`);
  console.log(`Deleting ${filesToDelete.length} old files`);
  
  let deletedCount = 0;
  filesToDelete.forEach(file => {
    try {
      fs.unlinkSync(file.path);
      deletedCount++;
    } catch (error) {
      console.error(`Failed to delete ${file.name}:`, error.message);
    }
  });
  
  console.log(`✅ Successfully deleted ${deletedCount} files`);
  console.log(`📁 ${filesToKeep.length} files remaining in uploads directory`);
  
} catch (error) {
  console.error('❌ Error cleaning up thumbnails:', error.message);
}
