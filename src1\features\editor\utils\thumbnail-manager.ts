import { client } from "@/lib/hono";
import { generateThumbnail, uploadThumbnail } from "./thumbnail";
import { Editor } from "@/features/editor/types";

interface ThumbnailManagerOptions {
  width?: number;
  height?: number;
  quality?: number;
  format?: "image/jpeg" | "image/png";
}

/**
 * Centralized thumbnail management system that ensures:
 * 1. Each template has exactly one thumbnail file
 * 2. Deterministic file naming (thumbnail-{projectId}.jpg)
 * 3. Database consistency
 * 4. File replacement instead of creation
 */
export class ThumbnailManager {
  private static instance: ThumbnailManager;
  private generationQueue = new Map<string, Promise<string | null>>();

  private constructor() {}

  static getInstance(): ThumbnailManager {
    if (!ThumbnailManager.instance) {
      ThumbnailManager.instance = new ThumbnailManager();
    }
    return ThumbnailManager.instance;
  }

  /**
   * Generate and save a single thumbnail for a project
   * This method ensures only one thumbnail exists per project
   */
  async generateAndSaveThumbnail(
    editor: Editor,
    projectId: string,
    options: ThumbnailManagerOptions = {}
  ): Promise<string | null> {
    // Prevent concurrent thumbnail generation for the same project
    if (this.generationQueue.has(projectId)) {
      console.log(`⏳ Thumbnail generation already in progress for project: ${projectId}`);
      return this.generationQueue.get(projectId)!;
    }

    const generationPromise = this._generateThumbnailInternal(editor, projectId, options);
    this.generationQueue.set(projectId, generationPromise);

    try {
      const result = await generationPromise;
      return result;
    } finally {
      this.generationQueue.delete(projectId);
    }
  }

  private async _generateThumbnailInternal(
    editor: Editor,
    projectId: string,
    options: ThumbnailManagerOptions
  ): Promise<string | null> {
    try {
      console.log(`🎨 Starting thumbnail generation for project: ${projectId}`);

      if (!editor?.canvas) {
        console.error("No canvas available for thumbnail generation");
        return null;
      }

      // Generate thumbnail data URL
      const thumbnailDataUrl = generateThumbnail(editor.canvas, {
        width: options.width || 300,
        height: options.height || 200,
        quality: options.quality || 0.7,
        format: options.format || "image/jpeg",
      });

      if (!thumbnailDataUrl) {
        console.error("Failed to generate thumbnail data URL");
        return null;
      }

      // Upload thumbnail with deterministic naming
      // This will replace any existing thumbnail file
      const thumbnailUrl = await uploadThumbnail(thumbnailDataUrl, projectId);

      if (!thumbnailUrl) {
        console.error("Failed to upload thumbnail");
        // Fallback: store as data URL if upload fails
        return this._fallbackToDataUrl(projectId, thumbnailDataUrl);
      }

      // Update database with the consistent thumbnail URL
      const success = await this._updateDatabaseThumbnail(projectId, thumbnailUrl);
      
      if (success) {
        console.log(`✅ Thumbnail successfully generated and saved for project: ${projectId}`);
        return thumbnailUrl;
      } else {
        console.error("Failed to update database with thumbnail URL");
        return null;
      }

    } catch (error) {
      console.error(`❌ Error generating thumbnail for project ${projectId}:`, error);
      return null;
    }
  }

  /**
   * Check if a project needs a thumbnail
   * Returns true if thumbnail is missing or null in database
   */
  async needsThumbnail(projectId: string): Promise<boolean> {
    try {
      const response = await client.api.projects[":id"].$get({
        param: { id: projectId },
      });

      if (!response.ok) {
        console.error("Failed to fetch project for thumbnail check");
        return true; // Assume it needs a thumbnail if we can't check
      }

      const project = await response.json();
      const hasValidThumbnail = project.data?.thumbnailUrl && 
                               project.data.thumbnailUrl.trim() !== '' &&
                               !project.data.thumbnailUrl.startsWith('data:'); // Exclude base64 data URLs

      return !hasValidThumbnail;
    } catch (error) {
      console.error("Error checking if project needs thumbnail:", error);
      return true; // Assume it needs a thumbnail if check fails
    }
  }

  /**
   * Get the expected thumbnail URL for a project
   */
  getExpectedThumbnailUrl(projectId: string): string {
    return `/uploads/thumbnail-${projectId}.jpg`;
  }

  private async _updateDatabaseThumbnail(projectId: string, thumbnailUrl: string): Promise<boolean> {
    try {
      const response = await client.api.projects[":id"].$patch({
        param: { id: projectId },
        json: {
          thumbnailUrl: thumbnailUrl,
        },
      });

      return response.ok;
    } catch (error) {
      console.error("Error updating database with thumbnail URL:", error);
      return false;
    }
  }

  private async _fallbackToDataUrl(projectId: string, dataUrl: string): Promise<string | null> {
    try {
      console.log("Falling back to data URL storage...");
      const response = await client.api.projects[":id"].$patch({
        param: { id: projectId },
        json: {
          thumbnailUrl: dataUrl,
        },
      });

      if (response.ok) {
        console.log("Fallback thumbnail storage successful");
        return dataUrl;
      } else {
        console.error("Fallback thumbnail storage also failed");
        return null;
      }
    } catch (error) {
      console.error("Error in fallback thumbnail storage:", error);
      return null;
    }
  }

  /**
   * Clean up old thumbnail files that don't match the deterministic naming pattern
   * This should be called during migration
   */
  async cleanupOldThumbnails(): Promise<void> {
    // This would be implemented as part of the migration script
    console.log("🧹 Cleanup of old thumbnails should be handled by migration script");
  }
}

// Export singleton instance
export const thumbnailManager = ThumbnailManager.getInstance();
