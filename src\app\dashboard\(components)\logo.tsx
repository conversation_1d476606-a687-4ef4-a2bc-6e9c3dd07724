﻿import Link from "next/link";
import Image from "next/image";
import { Space_Grotesk } from "next/font/google";

import { cn } from "@/lib/utils";

const font = Space_Grotesk({
  weight: ["700"],
  subsets: ["latin"],
  display: 'swap',
  variable: '--font-space-grotesk',
});

export const Logo = () => {
  return (
    <Link href="/dashboard">
      <div className="flex items-center gap-x-2 hover:opacity-75 transition h-[68px] px-4">
        <div className="size-8 relative">
          <Image src="/logo.svg" alt="The Canvas" fill priority />
        </div>
        <h1 className={cn(font.className, "text-xl font-bold")}>The Canvas</h1>
      </div>
    </Link>
  );
};
